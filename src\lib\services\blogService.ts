import { query, transaction } from '../database/connection';
import { 
  BlogPost, 
  Category, 
  Author, 
  Tag, 
  CreateBlogPostRequest, 
  UpdateBlogPostRequest, 
  BlogPostFilters, 
  BlogPostsResponse,
  CreateCommentRequest,
  Comment
} from '@/types/blog';

export class BlogService {
  // Categories
  static async getCategories(): Promise<Category[]> {
    const result = await query(`
      SELECT * FROM categories 
      ORDER BY name ASC
    `);
    return result.rows;
  }

  static async getCategoryBySlug(slug: string): Promise<Category | null> {
    const result = await query(`
      SELECT * FROM categories WHERE slug = $1
    `, [slug]);
    return result.rows[0] || null;
  }

  // Authors
  static async getAuthors(): Promise<Author[]> {
    const result = await query(`
      SELECT * FROM authors 
      WHERE is_active = true
      ORDER BY name ASC
    `);
    return result.rows;
  }

  static async getAuthorById(id: string): Promise<Author | null> {
    const result = await query(`
      SELECT * FROM authors WHERE id = $1
    `, [id]);
    return result.rows[0] || null;
  }

  // Tags
  static async getTags(): Promise<Tag[]> {
    const result = await query(`
      SELECT * FROM tags 
      ORDER BY name ASC
    `);
    return result.rows;
  }

  static async getTagsByPostId(postId: string): Promise<Tag[]> {
    const result = await query(`
      SELECT t.* FROM tags t
      JOIN blog_post_tags bpt ON t.id = bpt.tag_id
      WHERE bpt.blog_post_id = $1
      ORDER BY t.name ASC
    `, [postId]);
    return result.rows;
  }

  // Blog Posts
  static async getBlogPosts(filters: BlogPostFilters = {}): Promise<BlogPostsResponse> {
    const {
      status = 'published',
      category_id,
      author_id,
      tag_id,
      is_featured,
      search,
      limit = 10,
      offset = 0,
      sort_by = 'published_at',
      sort_order = 'desc'
    } = filters;

    let whereConditions = ['bp.status = $1'];
    let params: any[] = [status];
    let paramIndex = 2;

    if (category_id) {
      whereConditions.push(`bp.category_id = $${paramIndex}`);
      params.push(category_id);
      paramIndex++;
    }

    if (author_id) {
      whereConditions.push(`bp.author_id = $${paramIndex}`);
      params.push(author_id);
      paramIndex++;
    }

    if (is_featured !== undefined) {
      whereConditions.push(`bp.is_featured = $${paramIndex}`);
      params.push(is_featured);
      paramIndex++;
    }

    if (search) {
      whereConditions.push(`(bp.title ILIKE $${paramIndex} OR bp.excerpt ILIKE $${paramIndex} OR bp.content ILIKE $${paramIndex})`);
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (tag_id) {
      whereConditions.push(`EXISTS (
        SELECT 1 FROM blog_post_tags bpt 
        WHERE bpt.blog_post_id = bp.id AND bpt.tag_id = $${paramIndex}
      )`);
      params.push(tag_id);
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');
    
    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM blog_posts bp
      WHERE ${whereClause}
    `, params);

    const total = parseInt(countResult.rows[0].total);

    // Get posts with joins
    const postsResult = await query(`
      SELECT 
        bp.*,
        a.name as author_name,
        a.email as author_email,
        a.bio as author_bio,
        a.avatar_url as author_avatar_url,
        c.name as category_name,
        c.slug as category_slug,
        c.color as category_color,
        c.icon as category_icon
      FROM blog_posts bp
      LEFT JOIN authors a ON bp.author_id = a.id
      LEFT JOIN categories c ON bp.category_id = c.id
      WHERE ${whereClause}
      ORDER BY bp.${sort_by} ${sort_order.toUpperCase()}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...params, limit, offset]);

    // Populate tags for each post
    const posts: BlogPost[] = await Promise.all(
      postsResult.rows.map(async (row) => {
        const tags = await this.getTagsByPostId(row.id);
        
        return {
          ...row,
          author: row.author_name ? {
            id: row.author_id,
            name: row.author_name,
            email: row.author_email,
            bio: row.author_bio,
            avatar_url: row.author_avatar_url,
            is_active: true,
            created_at: new Date(),
            updated_at: new Date()
          } : undefined,
          category: row.category_name ? {
            id: row.category_id,
            name: row.category_name,
            slug: row.category_slug,
            color: row.category_color,
            icon: row.category_icon,
            created_at: new Date(),
            updated_at: new Date()
          } : undefined,
          tags
        };
      })
    );

    const totalPages = Math.ceil(total / limit);
    const page = Math.floor(offset / limit) + 1;

    return {
      posts,
      total,
      page,
      limit,
      total_pages: totalPages
    };
  }

  static async getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
    const result = await query(`
      SELECT 
        bp.*,
        a.name as author_name,
        a.email as author_email,
        a.bio as author_bio,
        a.avatar_url as author_avatar_url,
        c.name as category_name,
        c.slug as category_slug,
        c.color as category_color,
        c.icon as category_icon
      FROM blog_posts bp
      LEFT JOIN authors a ON bp.author_id = a.id
      LEFT JOIN categories c ON bp.category_id = c.id
      WHERE bp.slug = $1 AND bp.status = 'published'
    `, [slug]);

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    const tags = await this.getTagsByPostId(row.id);

    return {
      ...row,
      author: row.author_name ? {
        id: row.author_id,
        name: row.author_name,
        email: row.author_email,
        bio: row.author_bio,
        avatar_url: row.author_avatar_url,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      } : undefined,
      category: row.category_name ? {
        id: row.category_id,
        name: row.category_name,
        slug: row.category_slug,
        color: row.category_color,
        icon: row.category_icon,
        created_at: new Date(),
        updated_at: new Date()
      } : undefined,
      tags
    };
  }

  static async getBlogPostById(id: string): Promise<BlogPost | null> {
    const result = await query(`
      SELECT 
        bp.*,
        a.name as author_name,
        a.email as author_email,
        a.bio as author_bio,
        a.avatar_url as author_avatar_url,
        c.name as category_name,
        c.slug as category_slug,
        c.color as category_color,
        c.icon as category_icon
      FROM blog_posts bp
      LEFT JOIN authors a ON bp.author_id = a.id
      LEFT JOIN categories c ON bp.category_id = c.id
      WHERE bp.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    const tags = await this.getTagsByPostId(row.id);

    return {
      ...row,
      author: row.author_name ? {
        id: row.author_id,
        name: row.author_name,
        email: row.author_email,
        bio: row.author_bio,
        avatar_url: row.author_avatar_url,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      } : undefined,
      category: row.category_name ? {
        id: row.category_id,
        name: row.category_name,
        slug: row.category_slug,
        color: row.category_color,
        icon: row.category_icon,
        created_at: new Date(),
        updated_at: new Date()
      } : undefined,
      tags
    };
  }

  // Helper method to generate slug from title
  static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/[đĐ]/g, 'd')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
      .replace(/^-|-$/g, '');
  }
}
