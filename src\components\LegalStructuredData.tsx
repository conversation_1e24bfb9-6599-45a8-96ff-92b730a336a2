interface LegalStructuredDataProps {
  pageType: 'privacy' | 'terms';
}

export default function LegalStructuredData({ pageType }: LegalStructuredDataProps) {
  const baseUrl = "https://tamlinh.com";
  
  const privacySchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "<PERSON><PERSON><PERSON>ch <PERSON>ả<PERSON>",
    "description": "<PERSON><PERSON>h sách bảo mật của Thần <PERSON>ố <PERSON> về việc thu thập, sử dụng và bảo vệ thông tin cá nhân người dùng.",
    "url": `${baseUrl}/privacy`,
    "inLanguage": "vi-VN",
    "isPartOf": {
      "@type": "WebSite",
      "name": "Thần Số H<PERSON>c",
      "url": baseUrl
    },
    "about": {
      "@type": "Thing",
      "name": "<PERSON><PERSON>h sách bảo mật",
      "description": "<PERSON><PERSON> định về bảo vệ thông tin cá nhân và quyền riêng tư"
    },
    "mainEntity": {
      "@type": "Article",
      "headline": "Chính Sách Bảo M<PERSON> - Thần Số <PERSON>ọc",
      "description": "Cam kết bảo vệ thông tin cá nhân và quyền riêng tư của người dùng khi sử dụng dịch vụ tâm linh miễn phí.",
      "author": {
        "@type": "Organization",
        "name": "Thần Số Học"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Thần Số Học",
        "logo": {
          "@type": "ImageObject",
          "url": `${baseUrl}/logo.png`
        }
      },
      "datePublished": new Date().toISOString(),
      "dateModified": new Date().toISOString()
    }
  };

  const termsSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Điều Khoản Sử Dụng",
    "description": "Điều khoản và quy định sử dụng dịch vụ Thần Số Học, quyền và trách nhiệm của người dùng.",
    "url": `${baseUrl}/terms`,
    "inLanguage": "vi-VN",
    "isPartOf": {
      "@type": "WebSite",
      "name": "Thần Số Học",
      "url": baseUrl
    },
    "about": {
      "@type": "Thing",
      "name": "Điều khoản sử dụng",
      "description": "Quy định và điều khoản khi sử dụng dịch vụ"
    },
    "mainEntity": {
      "@type": "Article",
      "headline": "Điều Khoản Sử Dụng - Thần Số Học",
      "description": "Quy định về việc sử dụng website và dịch vụ tâm linh, quyền và trách nhiệm của người dùng.",
      "author": {
        "@type": "Organization",
        "name": "Thần Số Học"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Thần Số Học",
        "logo": {
          "@type": "ImageObject",
          "url": `${baseUrl}/logo.png`
        }
      },
      "datePublished": new Date().toISOString(),
      "dateModified": new Date().toISOString()
    }
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Trang Chủ",
        "item": baseUrl
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": pageType === 'privacy' ? "Chính Sách Bảo Mật" : "Điều Khoản Sử Dụng",
        "item": `${baseUrl}/${pageType}`
      }
    ]
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Thần Số Học",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": pageType === 'privacy' ? "<EMAIL>" : "<EMAIL>",
      "availableLanguage": "Vietnamese"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "VN"
    }
  };

  const schema = pageType === 'privacy' ? privacySchema : termsSchema;

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(schema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
    </>
  );
}
