// Legacy interface for backward compatibility
export interface LegacyBlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: string;
  publishedAt: string;
  updatedAt?: string;
  category: 'numerology' | 'zodiac' | 'feng-shui' | 'dreams' | 'spirituality' | 'tips' | 'fengshui';
  tags?: string[];
  featuredImage?: string;
  isPublished: boolean;
  readTime?: number; // phút đọc
}

// Database entities
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon: string;
  created_at: Date;
  updated_at: Date;
}

export interface Author {
  id: string;
  name: string;
  email?: string;
  bio?: string;
  avatar_url?: string;
  social_links?: Record<string, string>;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Tag {
  id: string;
  name: string;
  slug: string;
  created_at: Date;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featured_image_url?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  author_id?: string;
  category_id?: string;
  status: 'draft' | 'published' | 'archived';
  is_featured: boolean;
  view_count: number;
  read_time: number;
  published_at?: Date;
  created_at: Date;
  updated_at: Date;

  // Populated fields (joins)
  author?: Author;
  category?: Category;
  tags?: Tag[];
}

export interface Comment {
  id: string;
  blog_post_id: string;
  parent_id?: string;
  author_name: string;
  author_email?: string;
  content: string;
  is_approved: boolean;
  created_at: Date;
  updated_at: Date;

  // Populated fields
  replies?: Comment[];
}

export interface BlogView {
  id: string;
  blog_post_id: string;
  ip_address?: string;
  user_agent?: string;
  referrer?: string;
  viewed_at: Date;
}

// API request/response types
export interface CreateBlogPostRequest {
  title: string;
  slug?: string;
  excerpt?: string;
  content: string;
  featured_image_url?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  author_id?: string;
  category_id?: string;
  status?: 'draft' | 'published' | 'archived';
  is_featured?: boolean;
  read_time?: number;
  tag_ids?: string[];
  published_at?: Date;
}

export interface UpdateBlogPostRequest extends Partial<CreateBlogPostRequest> {
  id: string;
}

export interface BlogPostFilters {
  status?: 'draft' | 'published' | 'archived';
  category_id?: string;
  author_id?: string;
  tag_id?: string;
  is_featured?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
  sort_by?: 'created_at' | 'published_at' | 'view_count' | 'title';
  sort_order?: 'asc' | 'desc';
}

export interface BlogPostsResponse {
  posts: BlogPost[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface CreateCommentRequest {
  blog_post_id: string;
  parent_id?: string;
  author_name: string;
  author_email?: string;
  content: string;
}

// Legacy interfaces for backward compatibility
export interface BlogCategory {
  id: string;
  name: string;
  description: string;
  slug: string;
}

export interface BlogAuthor {
  id: string;
  name: string;
  bio?: string;
  avatar?: string;
  socialLinks?: {
    facebook?: string;
    instagram?: string;
    email?: string;
  };
}
