import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Thần Số Học Pythagoras Ch<PERSON> | T<PERSON>h <PERSON>ố <PERSON>ệnh Miễn <PERSON> - <PERSON>â<PERSON>",
  description: "🔮 T<PERSON>h thần số học chính xác theo phương pháp <PERSON>. <PERSON>h<PERSON><PERSON> phá số mệnh (Life Path Number), t<PERSON><PERSON> c<PERSON>ch, tài năng và vận mệnh. Miễn phí 100% - Hơn 50,000 người tin tưởng.",
  keywords: "thần số học, numerology, số mệnh, life path number, pythagoras, t<PERSON>h số mệnh, số định mệnh, s<PERSON> linh hồn, s<PERSON> nhân c<PERSON>ch, t<PERSON><PERSON> cách theo số, vận mệnh, tà<PERSON> năng, miễn phí",
  openGraph: {
    title: "Thần Số Học Pythagoras Ch<PERSON> | <PERSON><PERSON><PERSON>ệnh Miễ<PERSON>",
    description: "🔮 Khám phá số mệnh và tính cách qua thần số họ<PERSON>gor<PERSON>. <PERSON><PERSON> tích chi tiết số đường đờ<PERSON>, đ<PERSON><PERSON> mệnh, linh hồn. Miễn phí 100%.",
    type: "website",
    locale: "vi_VN",
    siteName: "Tâm Linh - Thần Số Học",
    url: "https://tamlinh.com/numerology",
    images: [
      {
        url: "/numerology-og.jpg",
        width: 1200,
        height: 630,
        alt: "Thần Số Học Pythagoras - Tính Số Mệnh Chính Xác",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Thần Số Học Pythagoras - Tính Số Mệnh Miễn Phí",
    description: "🔮 Khám phá số mệnh và tính cách qua thần số học Pythagoras chính xác. Miễn phí 100%.",
    images: ["/numerology-og.jpg"],
  },
  alternates: {
    canonical: "https://tamlinh.com/numerology",
  },
};

export default function NumerologyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
