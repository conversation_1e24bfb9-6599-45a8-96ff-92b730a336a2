@import "tailwindcss";
@theme {
  --background: #050211;
  --foreground: #ffffff;
  --color-golden: #FFD700;
  --mystic-purple: #2A0845;
  --deep-purple: #1A0B2E;
  --cosmic-blue: #0D0617;
}


@media (prefers-color-scheme: dark) {
  :root {
    --background: #050211;
    --foreground: #ffffff;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #050211;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #FFD700, #FFA500);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #FFA500, #FFD700);
}

body {
  background: linear-gradient(135deg, 
    #050211 0%, 
    #0D0617 25%, 
    #1A0B2E 50%, 
    #0D0617 75%, 
    #050211 100%
  );
  background-attachment: fixed;
  min-height: 100vh;
}

/* Floating particles animation */
.floating-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: rgba(255, 215, 0, 0.4);
  border-radius: 50%;
  animation: float 20s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

/* Mystical glow effect */
.mystical-glow {
  box-shadow: 
    0 0 20px rgba(255, 215, 0, 0.3),
    0 0 40px rgba(255, 215, 0, 0.2),
    0 0 60px rgba(255, 215, 0, 0.1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}


.cosmic-card {
  background: linear-gradient(to bottom right, rgba(31, 41, 55, 0.5), rgba(17, 24, 39, 0.5));
  backdrop-filter: blur(8px);
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0,0,0,0.25), 0 2px 8px rgba(31, 41, 55, 0.15);
  border: 1px solid rgba(156, 163, 175, 0.2);
  height: fit-content;
  transition: all 0.3s ease;
}

.cosmic-card.hoverable:hover {
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 1.5rem;

  transform: translateY(-5px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(255, 215, 0, 0.2);
}

/* Pulse animation for important elements */
.pulse-golden {
  animation: pulse-golden 2s infinite;
}

@keyframes pulse-golden {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
  }
}

/* Mystical button */
.mystical-button {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  position: relative;
  overflow: hidden;
}

.mystical-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.mystical-button:hover::before {
  left: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Blog content styles */
.prose {
  max-width: none;
}

.prose h1 {
  color: #ffffff;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #FFD700;
}

.prose h2 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: bold;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h3 {
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: bold;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.8;
}

.prose strong {
  color: #FFD700;
  font-weight: bold;
}

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose blockquote {
  border-left: 4px solid #FFD700;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #e0e7ff;
}

/* Loading bar animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes loadingPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-shimmer {
  animation: shimmer 1.5s infinite linear;
}

.animate-loading-pulse {
  animation: loadingPulse 2s infinite ease-in-out;
}
