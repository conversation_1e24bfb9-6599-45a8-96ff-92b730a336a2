import { NextRequest, NextResponse } from 'next/server';
import { BlogService } from '@/lib/services/blogService';
import { query } from '@/lib/database/connection';

// GET /api/blog/[slug] - Get blog post by slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    
    const post = await BlogService.getBlogPostBySlug(slug);
    
    if (!post) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    // Increment view count
    await query(`
      UPDATE blog_posts 
      SET view_count = view_count + 1 
      WHERE id = $1
    `, [post.id]);

    // Track view
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || '';
    const referrer = request.headers.get('referer') || '';

    await query(`
      INSERT INTO blog_views (blog_post_id, ip_address, user_agent, referrer)
      VALUES ($1, $2, $3, $4)
    `, [post.id, clientIP, userAgent, referrer]);

    // Update the view count in the returned post
    post.view_count += 1;
    
    return NextResponse.json(post);
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog post' },
      { status: 500 }
    );
  }
}
