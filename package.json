{"name": "tamlinh-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:setup": "psql -h $DB_HOST -U $DB_USER -d $DB_NAME -f src/lib/database/schema.sql", "db:reset": "npm run db:setup"}, "dependencies": {"clsx": "^2.1.1", "next": "15.4.2", "pg": "^8.13.1", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.11.10", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}