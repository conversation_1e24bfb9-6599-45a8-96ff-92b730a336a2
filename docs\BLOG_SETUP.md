# Blog System Setup Guide

## Overview

Hệ thống blog đã đượ<PERSON> nâng cấp để sử dụng PostgreSQL database với đầy đủ tính năng CRUD và tương thích ngược với code hiện tại.

## Features

### ✅ **Database Features**
- PostgreSQL với connection pooling
- Full CRUD operations (Create, Read, Update, Delete)
- Categories, Tags, Authors management
- Comments system (ready for future implementation)
- View tracking and analytics
- SEO optimization (meta tags, slugs)

### ✅ **API Endpoints**
- `GET /api/blog` - Get blog posts with filters
- `POST /api/blog` - Create new blog post
- `PUT /api/blog` - Update blog post
- `DELETE /api/blog` - Delete blog post
- `GET /api/blog/[slug]` - Get single post by slug
- `GET /api/blog/categories` - Get all categories
- `GET /api/blog/tags` - Get all tags

### ✅ **Backward Compatibility**
- Legacy BlogPost interface maintained
- Existing components work without changes
- Automatic fallback to static data if database fails
- Adapter pattern for seamless migration

## Setup Instructions

### 1. **Database Setup**

#### Install PostgreSQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS
brew install postgresql
brew services start postgresql

# Windows
# Download from https://www.postgresql.org/download/windows/
```

#### Create Database
```bash
# Connect to PostgreSQL
sudo -u postgres psql

# Create database and user
CREATE DATABASE tamlinh_blog;
CREATE USER tamlinh_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE tamlinh_blog TO tamlinh_user;
\q
```

### 2. **Environment Configuration**

Copy `.env.example` to `.env.local`:
```bash
cp .env.example .env.local
```

Update `.env.local` with your database credentials:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tamlinh_blog
DB_USER=tamlinh_user
DB_PASSWORD=your_secure_password
NODE_ENV=development
```

### 3. **Install Dependencies**

```bash
npm install
# This will install pg and @types/pg for PostgreSQL support
```

### 4. **Initialize Database Schema**

```bash
# Run the schema setup
npm run db:setup

# Or manually:
psql -h localhost -U tamlinh_user -d tamlinh_blog -f src/lib/database/schema.sql
```

### 5. **Start Development Server**

```bash
npm run dev
```

## Usage Examples

### Creating a Blog Post via API

```javascript
const response = await fetch('/api/blog', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    title: 'Thần Số Học Cơ Bản',
    content: '# Nội dung bài viết...',
    excerpt: 'Tóm tắt bài viết',
    category_id: 'uuid-of-category',
    tag_ids: ['uuid-1', 'uuid-2'],
    status: 'published',
    is_featured: false,
    read_time: 5
  })
});
```

### Getting Blog Posts with Filters

```javascript
const response = await fetch('/api/blog?category_id=uuid&limit=10&offset=0');
const data = await response.json();
```

### Using in Components (Backward Compatible)

```tsx
import { getBlogPosts, getBlogPostBySlug } from '@/lib/blogData';

// In component
const posts = await getBlogPosts({ category: 'numerology', limit: 5 });
const post = await getBlogPostBySlug('my-post-slug');
```

## Database Schema

### Tables
- **categories** - Blog categories (Thần Số Học, Phong Thủy, etc.)
- **authors** - Blog authors and contributors
- **tags** - Post tags for better organization
- **blog_posts** - Main blog posts table
- **blog_post_tags** - Many-to-many relationship for post tags
- **comments** - Comments system (ready for implementation)
- **blog_views** - View tracking for analytics

### Key Features
- UUID primary keys for better security
- Automatic timestamps (created_at, updated_at)
- Soft delete capability
- SEO-friendly slugs
- Meta tags support
- View counting
- Featured posts support

## Migration from Static Data

The system automatically handles migration:

1. **Automatic Fallback**: If database is unavailable, falls back to static data
2. **Adapter Pattern**: Converts between new and legacy formats seamlessly
3. **Backward Compatibility**: All existing components continue to work
4. **Gradual Migration**: Can migrate posts one by one or all at once

## Best Practices

### 1. **Content Management**
- Use meaningful slugs for SEO
- Add meta descriptions for all posts
- Categorize posts properly
- Use tags for better discoverability

### 2. **Performance**
- Use pagination for large post lists
- Implement caching for frequently accessed posts
- Optimize images before uploading

### 3. **SEO**
- Include meta titles and descriptions
- Use proper heading structure (H1, H2, H3)
- Add alt text for images
- Use semantic HTML

### 4. **Security**
- Validate all inputs
- Use parameterized queries (already implemented)
- Implement rate limiting for API endpoints
- Add authentication for admin functions

## Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql

# Check connection
psql -h localhost -U tamlinh_user -d tamlinh_blog -c "SELECT 1;"
```

### Schema Issues
```bash
# Reset database schema
npm run db:reset
```

### Development Issues
```bash
# Check logs
npm run dev
# Look for database connection errors in console
```

## Future Enhancements

- [ ] Admin dashboard for content management
- [ ] Image upload and management
- [ ] Comment system implementation
- [ ] Email notifications
- [ ] RSS feed generation
- [ ] Full-text search
- [ ] Content versioning
- [ ] Multi-language support
