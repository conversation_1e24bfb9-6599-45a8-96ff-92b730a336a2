import Link from 'next/link';

interface RelatedServicesProps {
  currentPage?: string;
  group?: string;
}

export default function RelatedServices({ currentPage, group }: RelatedServicesProps) {
  const services = [
    {
      href: "/numerology",
      group: "numerology",
      icon: "🔮",
      title: "Thần Số Học Pythagoras",
      description: "Tính số mệnh và phân tích tính cách thú vị",
    },
    {
      href: "/name-analysis",
      group: "numerology",
      icon: "✍️",
      title: "<PERSON><PERSON><PERSON>ê<PERSON>",
      description: "<PERSON>ân tích tên tuổi theo thần số học",
    },
    {
      href: "/dream",
      icon: "💭",
      group: "divination",
      title: "Gi<PERSON>i Mã Giấc <PERSON>ơ",
      description: "Từ điển giấc mơ đầy đủ nhất",
    },
    {
      href: "/zodiac",
      group: "zodiac",
      icon: "⭐",
      title: "Cung Hoàng Đạo",
      description: "Tử vi 12 cung hoàng đạo thú vị",
    },
    {
      href: "/fengshui",
      group: "fengshui",
      icon: "🏠",
      title: "<PERSON><PERSON> Thủy",
      description: "Bố trí nhà cửa theo phong thủy",
    },
    {
      href: "/numbers/meaning",
      group: "divination",
      icon: "🔢",
      title: "Ý Nghĩa Các Con Số",
      description: "Tìm hiểu ý nghĩa tâm linh, phong thủy và năng lượng của từng con số",
    },
    {
      href: "/cards",
      group: "divination",
      icon: "🃏",
      title: "Bói Bài Tây Vui",
      description: "Tử vi theo bài tây vui",
    },
    {
      href: "/tarot",
      group: "divination",
      icon: "🃏",
      title: "Bói Bài Tarot",
      description: "Tử vi theo bài tarot",
    },
    {
      href: "/games",
      group: "divination",
      icon: "🎮",
      title: "Minigame Bói",
      description: "Bói hình, màu, số để biết vận may",
    },
    {
      href: "/blog",
      group: "blog",
      icon: "📚",
      title: "Blog Kiến Thức",
      description: "Tìm hiểu thêm về các chủ đề huyền học",
    },

  ];

  // Filter out the current page and limit to 3 services
  const filteredServices = services
    .filter((service) => service.href !== currentPage && service.group === group)
    .slice(0, 3);

  return (
    <section className="py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">
            Chủ Đề Liên Quan
          </h2>
          <p className="text-gray-300">
            Khám phá thêm các chủ đề khác để hiểu rõ hơn về bản thân
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {filteredServices.map((service, index) => (
            <Link key={index} href={service.href} className="group">
              <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/20 hover:border-golden/50 transition-all duration-300 hover:scale-105 text-center">
                <div className="text-3xl mb-3 group-hover:scale-110 transition-transform">
                  {service.icon}
                </div>
                <h3 className="text-lg font-bold text-white mb-2">
                  {service.title}
                </h3>
                <p className="text-gray-400 text-sm">{service.description}</p>
              </div>
            </Link>
          ))}
        </div>

        {/* Call to Action */}
        {/* <div className="text-center mt-12">
          <div className="bg-gradient-to-br from-gray-900/40 to-gray-800/40 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/20">
            <h3 className="text-2xl font-bold text-white mb-4">
              <span className="bg-gradient-to-r from-golden to-yellow-400 bg-clip-text text-transparent">
                Tất Cả Nội Dung Đều Miễn Phí 100%
              </span>
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Chúng mình cam kết chia sẻ tất cả nội dung Giải Mã Tâm Linh chất
              lượng cao hoàn toàn miễn phí. Hãy khám phá và tìm hiểu về bản thân
              ngay hôm nay!
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                href="/numerology"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-golden to-yellow-300 text-gray-900 font-bold rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300"
              >
                <span>Tính Số Mệnh</span>
              </Link>
              <Link
                href="/zodiac"
                className="inline-flex items-center px-6 py-3 border border-white text-white font-bold rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300"
              >
                <span>Xem Cung Hoàng Đạo</span>
              </Link>
            </div>
          </div>
        </div> */}
      </div>
    </section>
  );
}
