import { NextRequest, NextResponse } from 'next/server';
import { BlogService } from '@/lib/services/blogService';
import { CreateBlogPostRequest, BlogPostFilters } from '@/types/blog';
import { query, transaction } from '@/lib/database/connection';

// GET /api/blog - Get blog posts with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const filters: BlogPostFilters = {
      status: (searchParams.get('status') as any) || 'published',
      category_id: searchParams.get('category_id') || undefined,
      author_id: searchParams.get('author_id') || undefined,
      tag_id: searchParams.get('tag_id') || undefined,
      is_featured: searchParams.get('is_featured') === 'true' ? true : 
                   searchParams.get('is_featured') === 'false' ? false : undefined,
      search: searchParams.get('search') || undefined,
      limit: parseInt(searchParams.get('limit') || '10'),
      offset: parseInt(searchParams.get('offset') || '0'),
      sort_by: (searchParams.get('sort_by') as any) || 'published_at',
      sort_order: (searchParams.get('sort_order') as any) || 'desc'
    };

    const result = await BlogService.getBlogPosts(filters);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    );
  }
}

// POST /api/blog - Create new blog post
export async function POST(request: NextRequest) {
  try {
    const body: CreateBlogPostRequest = await request.json();
    
    // Validate required fields
    if (!body.title || !body.content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      );
    }

    // Generate slug if not provided
    if (!body.slug) {
      body.slug = BlogService.generateSlug(body.title);
    }

    // Check if slug already exists
    const existingPost = await BlogService.getBlogPostBySlug(body.slug);
    if (existingPost) {
      return NextResponse.json(
        { error: 'A post with this slug already exists' },
        { status: 409 }
      );
    }

    const result = await transaction(async (client) => {
      // Insert blog post
      const postResult = await client.query(`
        INSERT INTO blog_posts (
          title, slug, excerpt, content, featured_image_url,
          meta_title, meta_description, meta_keywords,
          author_id, category_id, status, is_featured, read_time, published_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        RETURNING *
      `, [
        body.title,
        body.slug,
        body.excerpt,
        body.content,
        body.featured_image_url,
        body.meta_title || body.title,
        body.meta_description || body.excerpt,
        body.meta_keywords,
        body.author_id,
        body.category_id,
        body.status || 'draft',
        body.is_featured || false,
        body.read_time || 5,
        body.status === 'published' ? (body.published_at || new Date()) : null
      ]);

      const post = postResult.rows[0];

      // Insert tags if provided
      if (body.tag_ids && body.tag_ids.length > 0) {
        for (const tagId of body.tag_ids) {
          await client.query(`
            INSERT INTO blog_post_tags (blog_post_id, tag_id)
            VALUES ($1, $2)
            ON CONFLICT DO NOTHING
          `, [post.id, tagId]);
        }
      }

      return post;
    });

    // Fetch the complete post with relations
    const newPost = await BlogService.getBlogPostById(result.id);
    
    return NextResponse.json(newPost, { status: 201 });
  } catch (error) {
    console.error('Error creating blog post:', error);
    return NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    );
  }
}

// PUT /api/blog - Update blog post
export async function PUT(request: NextRequest) {
  try {
    const body: { id: string } & Partial<CreateBlogPostRequest> = await request.json();
    
    if (!body.id) {
      return NextResponse.json(
        { error: 'Post ID is required' },
        { status: 400 }
      );
    }

    // Check if post exists
    const existingPost = await BlogService.getBlogPostById(body.id);
    if (!existingPost) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    // If slug is being changed, check for conflicts
    if (body.slug && body.slug !== existingPost.slug) {
      const conflictPost = await BlogService.getBlogPostBySlug(body.slug);
      if (conflictPost && conflictPost.id !== body.id) {
        return NextResponse.json(
          { error: 'A post with this slug already exists' },
          { status: 409 }
        );
      }
    }

    const result = await transaction(async (client) => {
      // Build update query dynamically
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (body.title !== undefined) {
        updateFields.push(`title = $${paramIndex}`);
        updateValues.push(body.title);
        paramIndex++;
      }

      if (body.slug !== undefined) {
        updateFields.push(`slug = $${paramIndex}`);
        updateValues.push(body.slug);
        paramIndex++;
      }

      if (body.excerpt !== undefined) {
        updateFields.push(`excerpt = $${paramIndex}`);
        updateValues.push(body.excerpt);
        paramIndex++;
      }

      if (body.content !== undefined) {
        updateFields.push(`content = $${paramIndex}`);
        updateValues.push(body.content);
        paramIndex++;
      }

      if (body.featured_image_url !== undefined) {
        updateFields.push(`featured_image_url = $${paramIndex}`);
        updateValues.push(body.featured_image_url);
        paramIndex++;
      }

      if (body.meta_title !== undefined) {
        updateFields.push(`meta_title = $${paramIndex}`);
        updateValues.push(body.meta_title);
        paramIndex++;
      }

      if (body.meta_description !== undefined) {
        updateFields.push(`meta_description = $${paramIndex}`);
        updateValues.push(body.meta_description);
        paramIndex++;
      }

      if (body.meta_keywords !== undefined) {
        updateFields.push(`meta_keywords = $${paramIndex}`);
        updateValues.push(body.meta_keywords);
        paramIndex++;
      }

      if (body.author_id !== undefined) {
        updateFields.push(`author_id = $${paramIndex}`);
        updateValues.push(body.author_id);
        paramIndex++;
      }

      if (body.category_id !== undefined) {
        updateFields.push(`category_id = $${paramIndex}`);
        updateValues.push(body.category_id);
        paramIndex++;
      }

      if (body.status !== undefined) {
        updateFields.push(`status = $${paramIndex}`);
        updateValues.push(body.status);
        paramIndex++;

        // Set published_at when status changes to published
        if (body.status === 'published' && existingPost.status !== 'published') {
          updateFields.push(`published_at = $${paramIndex}`);
          updateValues.push(body.published_at || new Date());
          paramIndex++;
        }
      }

      if (body.is_featured !== undefined) {
        updateFields.push(`is_featured = $${paramIndex}`);
        updateValues.push(body.is_featured);
        paramIndex++;
      }

      if (body.read_time !== undefined) {
        updateFields.push(`read_time = $${paramIndex}`);
        updateValues.push(body.read_time);
        paramIndex++;
      }

      // Always update the updated_at field
      updateFields.push(`updated_at = NOW()`);

      if (updateFields.length === 1) { // Only updated_at
        return existingPost;
      }

      // Update the post
      updateValues.push(body.id);
      const postResult = await client.query(`
        UPDATE blog_posts 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `, updateValues);

      const post = postResult.rows[0];

      // Update tags if provided
      if (body.tag_ids !== undefined) {
        // Remove existing tags
        await client.query(`
          DELETE FROM blog_post_tags WHERE blog_post_id = $1
        `, [body.id]);

        // Insert new tags
        if (body.tag_ids.length > 0) {
          for (const tagId of body.tag_ids) {
            await client.query(`
              INSERT INTO blog_post_tags (blog_post_id, tag_id)
              VALUES ($1, $2)
            `, [post.id, tagId]);
          }
        }
      }

      return post;
    });

    // Fetch the complete updated post with relations
    const updatedPost = await BlogService.getBlogPostById(result.id);
    
    return NextResponse.json(updatedPost);
  } catch (error) {
    console.error('Error updating blog post:', error);
    return NextResponse.json(
      { error: 'Failed to update blog post' },
      { status: 500 }
    );
  }
}

// DELETE /api/blog - Delete blog post
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Post ID is required' },
        { status: 400 }
      );
    }

    // Check if post exists
    const existingPost = await BlogService.getBlogPostById(id);
    if (!existingPost) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    await query(`DELETE FROM blog_posts WHERE id = $1`, [id]);
    
    return NextResponse.json({ message: 'Post deleted successfully' });
  } catch (error) {
    console.error('Error deleting blog post:', error);
    return NextResponse.json(
      { error: 'Failed to delete blog post' },
      { status: 500 }
    );
  }
}
