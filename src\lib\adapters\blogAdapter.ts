import { BlogPost, LegacyBlogPost } from '@/types/blog';
import { BlogService } from '@/lib/services/blogService';

/**
 * Adapter to convert between new BlogPost format and legacy format
 * This maintains backward compatibility with existing components
 */
export class BlogAdapter {
  /**
   * Convert new BlogPost to legacy format
   */
  static toLegacyFormat(post: BlogPost): LegacyBlogPost {
    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt || '',
      content: post.content,
      author: post.author?.name || 'Unknown',
      publishedAt: post.published_at?.toISOString() || post.created_at.toISOString(),
      updatedAt: post.updated_at.toISOString(),
      category: this.mapCategoryToLegacy(post.category?.slug),
      tags: post.tags?.map(tag => tag.name) || [],
      featuredImage: post.featured_image_url,
      isPublished: post.status === 'published',
      readTime: post.read_time
    };
  }

  /**
   * Convert legacy BlogPost to new format
   */
  static fromLegacyFormat(legacyPost: LegacyBlogPost): Partial<BlogPost> {
    return {
      id: legacyPost.id,
      title: legacyPost.title,
      slug: legacyPost.slug,
      excerpt: legacyPost.excerpt,
      content: legacyPost.content,
      featured_image_url: legacyPost.featuredImage,
      status: legacyPost.isPublished ? 'published' : 'draft',
      is_featured: false,
      view_count: 0,
      read_time: legacyPost.readTime || 5,
      published_at: legacyPost.isPublished ? new Date(legacyPost.publishedAt) : undefined,
      created_at: new Date(legacyPost.publishedAt),
      updated_at: new Date(legacyPost.updatedAt || legacyPost.publishedAt)
    };
  }

  /**
   * Map category slug to legacy category format
   */
  private static mapCategoryToLegacy(categorySlug?: string): LegacyBlogPost['category'] {
    const mapping: Record<string, LegacyBlogPost['category']> = {
      'than-so-hoc': 'numerology',
      'cung-hoang-dao': 'zodiac',
      'phong-thuy': 'fengshui',
      'giai-ma-giac-mo': 'dreams',
      'tam-linh': 'spirituality',
      'huong-dan': 'tips'
    };

    return mapping[categorySlug || ''] || 'spirituality';
  }

  /**
   * Map legacy category to new category slug
   */
  private static mapLegacyToCategory(legacyCategory: LegacyBlogPost['category']): string {
    const mapping: Record<LegacyBlogPost['category'], string> = {
      'numerology': 'than-so-hoc',
      'zodiac': 'cung-hoang-dao',
      'fengshui': 'phong-thuy',
      'feng-shui': 'phong-thuy',
      'dreams': 'giai-ma-giac-mo',
      'spirituality': 'tam-linh',
      'tips': 'huong-dan'
    };

    return mapping[legacyCategory] || 'tam-linh';
  }

  /**
   * Get blog posts in legacy format for backward compatibility
   */
  static async getLegacyBlogPosts(filters?: {
    category?: string;
    limit?: number;
    offset?: number;
  }): Promise<LegacyBlogPost[]> {
    try {
      const blogFilters = {
        status: 'published' as const,
        category_id: filters?.category ? await this.getCategoryIdByLegacyName(filters.category) : undefined,
        limit: filters?.limit || 10,
        offset: filters?.offset || 0
      };

      const result = await BlogService.getBlogPosts(blogFilters);
      return result.posts.map(post => this.toLegacyFormat(post));
    } catch (error) {
      console.error('Error fetching legacy blog posts:', error);
      return [];
    }
  }

  /**
   * Get single blog post in legacy format
   */
  static async getLegacyBlogPostBySlug(slug: string): Promise<LegacyBlogPost | null> {
    try {
      const post = await BlogService.getBlogPostBySlug(slug);
      return post ? this.toLegacyFormat(post) : null;
    } catch (error) {
      console.error('Error fetching legacy blog post:', error);
      return null;
    }
  }

  /**
   * Helper to get category ID by legacy category name
   */
  private static async getCategoryIdByLegacyName(legacyCategory: string): Promise<string | undefined> {
    try {
      const categorySlug = this.mapLegacyToCategory(legacyCategory as LegacyBlogPost['category']);
      const category = await BlogService.getCategoryBySlug(categorySlug);
      return category?.id;
    } catch (error) {
      console.error('Error getting category ID:', error);
      return undefined;
    }
  }

  /**
   * Get category name for display (legacy format)
   */
  static getCategoryDisplayName(categorySlug?: string): string {
    const mapping: Record<string, string> = {
      'than-so-hoc': 'Thần Số Học',
      'cung-hoang-dao': 'Cung Hoàng Đạo',
      'phong-thuy': 'Phong Thủy',
      'giai-ma-giac-mo': 'Giải Mã Giấc Mơ',
      'tam-linh': 'Tâm Linh',
      'huong-dan': 'Hướng Dẫn'
    };

    return mapping[categorySlug || ''] || 'Tâm Linh';
  }

  /**
   * Get all categories in legacy format
   */
  static async getLegacyCategories(): Promise<Array<{ id: string; name: string; slug: string }>> {
    try {
      const categories = await BlogService.getCategories();
      return categories.map(cat => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug
      }));
    } catch (error) {
      console.error('Error fetching legacy categories:', error);
      return [];
    }
  }
}
